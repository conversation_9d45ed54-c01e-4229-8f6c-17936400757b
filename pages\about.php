<?php
/**
 * About Page
 *
 * This file contains the about page content.
 *
 * @package Pharaoh Finance Private and Fast Loans
 */

// Define LENDSWIFT constant to prevent direct access to included files
if (!defined('LENDSWIFT')) {
    define('LENDSWIFT', true);
}

// Include initialization file
if (!defined('LENDSWIFT')) {
    // If accessed directly, use relative path
    require_once '../includes/init.php';
}
?>

<div class="about-wrapper">
    <!-- Hero Section with Image -->
    <section class="about-main-hero" style="background-image: linear-gradient(rgba(79, 70, 229, 0.8), rgba(79, 70, 229, 0.8)), url('<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 020.webp');">
        <div class="hero-content-wrapper">
            <div class="hero-text-content">
                <span class="hero-badge">Our Company</span>
                <h1>About <?php echo SITE_NAME; ?></h1>
                <p>Empowering financial dreams through innovative loan solutions. We're dedicated to providing fast, secure, and transparent financial services that help our customers achieve their goals.</p>
                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number">50,000+</div>
                        <div class="stat-label">Happy Customers</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">$500M+</div>
                        <div class="stat-label">Loans Funded</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">4.9/5</div>
                        <div class="stat-label">Customer Rating</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="about-hero-section section-light">
        <div class="section-container">
            <div class="about-hero-content">
                <span class="section-badge">Our Story</span>
                <h2>Building Trust Through Innovation</h2>
                <p class="about-description">Since our founding, we've been committed to revolutionizing the lending industry through cutting-edge technology and customer-centric solutions.</p>
            </div>
            <div class="about-hero-image">
                <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 021.webp" alt="About Our Company" style="border-radius: 1rem; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);">
                <div class="about-shape-1">
                    <svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="100" cy="100" r="100" fill="url(#about-gradient-1)" fill-opacity="0.1"/>
                        <defs>
                            <linearGradient id="about-gradient-1" x1="0" y1="0" x2="200" y2="200" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#4F46E5"/>
                                <stop offset="1" stop-color="#3B82F6"/>
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
                <div class="about-shape-2">
                    <svg width="150" height="150" viewBox="0 0 150 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="75" cy="75" r="75" fill="url(#about-gradient-2)" fill-opacity="0.1"/>
                        <defs>
                            <linearGradient id="about-gradient-2" x1="0" y1="0" x2="150" y2="150" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#3B82F6"/>
                                <stop offset="1" stop-color="#4F46E5"/>
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
            </div>
        </div>
    </section>

    <section class="about-content-section section-darker">
        <div class="section-container">
            <div class="section-header">
                <span class="section-badge">About Our Company</span>
                <h2>Discover <?php echo SITE_NAME; ?></h2>
                <p>Learn more about our mission, values, and the team behind our success</p>
            </div>

            <div class="about-tabs-container">
                <div class="about-tabs-navigation">
                    <div class="about-tabs-header">
                        <h3>Company Information</h3>
                    </div>
                    <div class="about-tabs">
                        <button class="about-tab active" data-tab="mission">
                            <div class="tab-number"><i class="fas fa-bullseye"></i></div>
                            <span>Our Mission</span>
                        </button>
                        <button class="about-tab" data-tab="story">
                            <div class="tab-number"><i class="fas fa-history"></i></div>
                            <span>Our Story</span>
                        </button>
                        <button class="about-tab" data-tab="values">
                            <div class="tab-number"><i class="fas fa-star"></i></div>
                            <span>Our Values</span>
                        </button>
                        <button class="about-tab" data-tab="performance">
                            <div class="tab-number"><i class="fas fa-chart-bar"></i></div>
                            <span>Performance Dashboard</span>
                        </button>
                    </div>
                </div>

                <div class="about-tab-content-container">
                    <!-- Mission Tab Content -->
                    <div class="about-tab-content active" id="mission-content">
                        <div class="about-section-header">
                            <div class="progress-circle">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <h2>Our Mission</h2>
                        </div>
                        <div class="mission-content-wrapper">
                            <div class="about-section-content">
                                <p>At <?php echo SITE_NAME; ?>, our mission is to provide a seamless and transparent loan management experience for both borrowers and administrators. We believe in making the loan application process simple, efficient, and accessible to everyone.</p>
                                <p>We are committed to:</p>
                                <ul class="mission-list">
                                    <li>
                                        <div class="list-icon">
                                            <div class="progress-circle">
                                                <i class="fas fa-check"></i>
                                            </div>
                                        </div>
                                        <span>Providing a user-friendly platform for loan applications and management</span>
                                    </li>
                                    <li>
                                        <div class="list-icon">
                                            <div class="progress-circle">
                                                <i class="fas fa-check"></i>
                                            </div>
                                        </div>
                                        <span>Ensuring transparency in all loan terms and conditions</span>
                                    </li>
                                    <li>
                                        <div class="list-icon">
                                            <div class="progress-circle">
                                                <i class="fas fa-check"></i>
                                            </div>
                                        </div>
                                        <span>Offering competitive interest rates and flexible repayment options</span>
                                    </li>
                                    <li>
                                        <div class="list-icon">
                                            <div class="progress-circle">
                                                <i class="fas fa-check"></i>
                                            </div>
                                        </div>
                                        <span>Maintaining the highest standards of security for all user data</span>
                                    </li>
                                    <li>
                                        <div class="list-icon">
                                            <div class="progress-circle">
                                                <i class="fas fa-check"></i>
                                            </div>
                                        </div>
                                        <span>Delivering exceptional customer service and support</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="mission-image">
                                <svg width="100%" height="100%" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <!-- Background Circle -->
                                    <circle cx="200" cy="200" r="180" fill="#E0E7FF" opacity="0.5"/>

                                    <!-- Target Icon -->
                                    <circle cx="200" cy="200" r="150" stroke="#4F46E5" stroke-width="2" stroke-dasharray="4 4"/>
                                    <circle cx="200" cy="200" r="120" stroke="#4F46E5" stroke-width="2" stroke-dasharray="4 4"/>
                                    <circle cx="200" cy="200" r="90" stroke="#4F46E5" stroke-width="2" stroke-dasharray="4 4"/>
                                    <circle cx="200" cy="200" r="60" stroke="#4F46E5" stroke-width="2" stroke-dasharray="4 4"/>
                                    <circle cx="200" cy="200" r="30" fill="#4F46E5" opacity="0.8"/>

                                    <!-- Arrows -->
                                    <path d="M200 50 L200 100" stroke="#4F46E5" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M200 300 L200 350" stroke="#4F46E5" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M50 200 L100 200" stroke="#4F46E5" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M300 200 L350 200" stroke="#4F46E5" stroke-width="2" stroke-linecap="round"/>

                                    <!-- Arrow Heads -->
                                    <path d="M195 95 L200 100 L205 95" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M195 355 L200 350 L205 355" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M95 195 L100 200 L95 205" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M355 195 L350 200 L355 205" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>

                                    <!-- Checkmark -->
                                    <path d="M180 200 L190 210 L220 180" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Story Tab Content -->
                    <div class="about-tab-content" id="story-content">
                        <div class="about-section-header">
                            <div class="progress-circle">
                                <i class="fas fa-history"></i>
                            </div>
                            <h2>Our Story</h2>
                        </div>
                        <div class="about-section-content">
                            <p><?php echo SITE_NAME; ?> was founded in 2023 with a vision to revolutionize the loan management industry. We recognized the challenges faced by both borrowers and lenders in the traditional loan application process and set out to create a modern solution that addresses these pain points.</p>
                            <p>Our team of financial experts and technology professionals worked together to develop a comprehensive loan management system that simplifies the entire process from application to repayment. Today, we serve thousands of customers and continue to innovate and improve our platform based on user feedback and industry best practices.</p>

                            <div class="story-timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker">
                                        <div class="progress-circle">
                                            <span>1</span>
                                        </div>
                                    </div>
                                    <div class="timeline-content">
                                        <h3>2023: Foundation</h3>
                                        <p><?php echo SITE_NAME; ?> was established with the goal of simplifying loan management for everyone.</p>
                                    </div>
                                </div>

                                <div class="timeline-item">
                                    <div class="timeline-marker">
                                        <div class="progress-circle">
                                            <span>2</span>
                                        </div>
                                    </div>
                                    <div class="timeline-content">
                                        <h3>2023: Platform Launch</h3>
                                        <p>We launched our first version of the loan management platform after months of development and testing.</p>
                                    </div>
                                </div>

                                <div class="timeline-item">
                                    <div class="timeline-marker">
                                        <div class="progress-circle">
                                            <span>3</span>
                                        </div>
                                    </div>
                                    <div class="timeline-content">
                                        <h3>2023: Expansion</h3>
                                        <p>We expanded our services to include multiple loan products and enhanced features for both borrowers and administrators.</p>
                                    </div>
                                </div>

                                <div class="timeline-item">
                                    <div class="timeline-marker">
                                        <div class="progress-circle">
                                            <span>4</span>
                                        </div>
                                    </div>
                                    <div class="timeline-content">
                                        <h3>Today: Continuous Innovation</h3>
                                        <p>We continue to innovate and improve our platform, focusing on user experience, security, and efficiency.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Values Tab Content -->
                    <div class="about-tab-content" id="values-content">
                        <div class="about-section-header">
                            <div class="progress-circle">
                                <i class="fas fa-star"></i>
                            </div>
                            <h2>Our Values</h2>
                        </div>
                        <div class="about-section-content">
                            <p>Our core values guide everything we do at <?php echo SITE_NAME; ?>. They shape our culture, influence our decisions, and define how we interact with our customers and partners.</p>

                            <div class="values-grid">
                                <div class="value-card">
                                    <div class="value-icon">
                                        <div class="progress-circle">
                                            <i class="fas fa-lock"></i>
                                        </div>
                                    </div>
                                    <div class="value-content">
                                        <h3>Security</h3>
                                        <p>We prioritize the security of your personal and financial information with state-of-the-art encryption and data protection measures.</p>
                                        <ul class="value-features">
                                            <li><i class="fas fa-check"></i> Bank-level encryption</li>
                                            <li><i class="fas fa-check"></i> Secure document storage</li>
                                            <li><i class="fas fa-check"></i> Regular security audits</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="value-card">
                                    <div class="value-icon">
                                        <div class="progress-circle">
                                            <i class="fas fa-handshake"></i>
                                        </div>
                                    </div>
                                    <div class="value-content">
                                        <h3>Integrity</h3>
                                        <p>We operate with honesty and transparency in all our dealings, ensuring that our customers always know what to expect.</p>
                                        <ul class="value-features">
                                            <li><i class="fas fa-check"></i> Clear fee structure</li>
                                            <li><i class="fas fa-check"></i> Transparent terms</li>
                                            <li><i class="fas fa-check"></i> Honest communication</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="value-card">
                                    <div class="value-icon">
                                        <div class="progress-circle">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                    </div>
                                    <div class="value-content">
                                        <h3>Innovation</h3>
                                        <p>We continuously strive to improve our platform and services, embracing new technologies and methodologies.</p>
                                        <ul class="value-features">
                                            <li><i class="fas fa-check"></i> Cutting-edge technology</li>
                                            <li><i class="fas fa-check"></i> Continuous improvement</li>
                                            <li><i class="fas fa-check"></i> Forward-thinking solutions</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="value-card">
                                    <div class="value-icon">
                                        <div class="progress-circle">
                                            <i class="fas fa-users"></i>
                                        </div>
                                    </div>
                                    <div class="value-content">
                                        <h3>Customer Focus</h3>
                                        <p>We put our customers at the center of everything we do, designing our services to meet their needs and exceed their expectations.</p>
                                        <ul class="value-features">
                                            <li><i class="fas fa-check"></i> Responsive support</li>
                                            <li><i class="fas fa-check"></i> User-centered design</li>
                                            <li><i class="fas fa-check"></i> Feedback-driven improvements</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Dashboard Tab Content -->
                    <div class="about-tab-content" id="performance-content">
                        <div class="about-section-header">
                            <div class="progress-circle">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h2>Performance Dashboard</h2>
                        </div>
                        <div class="about-section-content">
                            <p>At <?php echo SITE_NAME; ?>, we believe in transparency and accountability. Our performance metrics demonstrate our commitment to providing fast, reliable, and secure loan services to our customers.</p>

                            <div class="dashboard-container">
                                <div class="dashboard-svg-container">
                                    <svg width="100%" height="300" viewBox="0 0 800 300" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <!-- Dashboard Background -->
                                        <rect width="800" height="300" rx="10" fill="#F9FAFB"/>

                                        <!-- Grid Lines -->
                                        <line x1="50" y1="250" x2="750" y2="250" stroke="#E5E7EB" stroke-width="1"/>
                                        <line x1="50" y1="200" x2="750" y2="200" stroke="#E5E7EB" stroke-width="1"/>
                                        <line x1="50" y1="150" x2="750" y2="150" stroke="#E5E7EB" stroke-width="1"/>
                                        <line x1="50" y1="100" x2="750" y2="100" stroke="#E5E7EB" stroke-width="1"/>
                                        <line x1="50" y1="50" x2="750" y2="50" stroke="#E5E7EB" stroke-width="1"/>

                                        <!-- Vertical Bars -->
                                        <rect x="100" y="100" width="40" height="150" rx="5" fill="#4F46E5" opacity="0.8"/>
                                        <rect x="200" y="80" width="40" height="170" rx="5" fill="#4F46E5" opacity="0.8"/>
                                        <rect x="300" y="120" width="40" height="130" rx="5" fill="#4F46E5" opacity="0.8"/>
                                        <rect x="400" y="70" width="40" height="180" rx="5" fill="#4F46E5" opacity="0.8"/>
                                        <rect x="500" y="50" width="40" height="200" rx="5" fill="#4F46E5" opacity="0.8"/>
                                        <rect x="600" y="90" width="40" height="160" rx="5" fill="#4F46E5" opacity="0.8"/>
                                        <rect x="700" y="30" width="40" height="220" rx="5" fill="#4F46E5" opacity="0.8"/>

                                        <!-- Line Chart -->
                                        <path d="M100 180 L200 150 L300 170 L400 130 L500 100 L600 120 L700 80" stroke="#10B981" stroke-width="3" fill="none"/>

                                        <!-- Data Points -->
                                        <circle cx="100" cy="180" r="6" fill="#10B981"/>
                                        <circle cx="200" cy="150" r="6" fill="#10B981"/>
                                        <circle cx="300" cy="170" r="6" fill="#10B981"/>
                                        <circle cx="400" cy="130" r="6" fill="#10B981"/>
                                        <circle cx="500" cy="100" r="6" fill="#10B981"/>
                                        <circle cx="600" cy="120" r="6" fill="#10B981"/>
                                        <circle cx="700" cy="80" r="6" fill="#10B981"/>

                                        <!-- Labels -->
                                        <text x="100" y="270" text-anchor="middle" font-size="12" fill="#6B7280">Jan</text>
                                        <text x="200" y="270" text-anchor="middle" font-size="12" fill="#6B7280">Feb</text>
                                        <text x="300" y="270" text-anchor="middle" font-size="12" fill="#6B7280">Mar</text>
                                        <text x="400" y="270" text-anchor="middle" font-size="12" fill="#6B7280">Apr</text>
                                        <text x="500" y="270" text-anchor="middle" font-size="12" fill="#6B7280">May</text>
                                        <text x="600" y="270" text-anchor="middle" font-size="12" fill="#6B7280">Jun</text>
                                        <text x="700" y="270" text-anchor="middle" font-size="12" fill="#6B7280">Jul</text>
                                    </svg>
                                </div>

                                <div class="metrics-grid">
                                    <div class="metric-card">
                                        <div class="metric-icon">
                                            <div class="progress-circle">
                                                <i class="fas fa-clock"></i>
                                            </div>
                                        </div>
                                        <div class="metric-content">
                                            <h3>Average Approval Time</h3>
                                            <div class="metric-value">24 Hours</div>
                                            <div class="metric-progress">
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 85%;"></div>
                                                </div>
                                                <span>85% faster than industry average</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="metric-card">
                                        <div class="metric-icon">
                                            <div class="progress-circle">
                                                <i class="fas fa-check-circle"></i>
                                            </div>
                                        </div>
                                        <div class="metric-content">
                                            <h3>Approval Rate</h3>
                                            <div class="metric-value">92%</div>
                                            <div class="metric-progress">
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 92%;"></div>
                                                </div>
                                                <span>Higher than industry standard</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="metric-card">
                                        <div class="metric-icon">
                                            <div class="progress-circle">
                                                <i class="fas fa-shield-alt"></i>
                                            </div>
                                        </div>
                                        <div class="metric-content">
                                            <h3>Security Rating</h3>
                                            <div class="metric-value">A+</div>
                                            <div class="metric-progress">
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 98%;"></div>
                                                </div>
                                                <span>Top-tier security protocols</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="metric-card">
                                        <div class="metric-icon">
                                            <div class="progress-circle">
                                                <i class="fas fa-smile"></i>
                                            </div>
                                        </div>
                                        <div class="metric-content">
                                            <h3>Customer Satisfaction</h3>
                                            <div class="metric-value">4.8/5</div>
                                            <div class="metric-progress">
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 96%;"></div>
                                                </div>
                                                <span>Based on 10,000+ reviews</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
    /* About Page Styles */
    .about-wrapper {
        width: 100%;
        overflow-x: hidden;
        margin: 0;
        padding: 0;
    }

    /* Main Hero Section with Background Image */
    .about-main-hero {
        padding: 8rem 0;
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        position: relative;
        color: white;
        text-align: center;
        min-height: 80vh;
        display: flex;
        align-items: center;
    }

    .hero-content-wrapper {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 4rem;
        width: 100%;
    }

    .hero-text-content {
        max-width: 800px;
        margin: 0 auto;
    }

    .hero-badge {
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        backdrop-filter: blur(10px);
    }

    .about-main-hero h1 {
        font-size: 4rem;
        font-weight: 800;
        margin-bottom: 1.5rem;
        line-height: 1.1;
    }

    .about-main-hero p {
        font-size: 1.25rem;
        margin-bottom: 3rem;
        opacity: 0.95;
        line-height: 1.6;
    }

    .hero-stats {
        display: flex;
        justify-content: center;
        gap: 4rem;
        flex-wrap: wrap;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        color: white;
    }

    .stat-label {
        font-size: 1rem;
        opacity: 0.9;
        color: white;
    }

    /* Secondary Hero Section */
    .about-hero-section {
        padding: 5rem 0;
        position: relative;
        overflow: hidden;
    }

    .about-hero-section .section-container {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    .about-hero-content {
        flex: 1;
        padding-right: 2rem;
    }

    .about-hero-content h1 {
        font-size: 3rem;
        color: var(--text-color);
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .about-description {
        font-size: 1.25rem;
        color: var(--text-muted);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .about-hero-image {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }

    .about-hero-image img {
        max-width: 100%;
        height: auto;
        position: relative;
        z-index: 2;
    }

    .about-shape-1, .about-shape-2 {
        position: absolute;
        z-index: 1;
    }

    .about-shape-1 {
        top: -50px;
        right: -30px;
    }

    .about-shape-2 {
        bottom: -30px;
        left: -20px;
    }

    /* About Content Section */
    .about-content-section {
        padding: 5rem 0;
    }

    .about-content-section .section-container {
        width: 100%;
    }

    .about-tabs-container {
        display: flex;
        flex-direction: column;
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .about-tabs-navigation {
        background-color: var(--primary-light);
        padding: 1.5rem;
        border-bottom: 1px solid var(--border-color);
    }

    .about-tabs-header {
        margin-bottom: 1rem;
    }

    .about-tabs-header h3 {
        font-size: 1.25rem;
        color: var(--primary-color);
        margin: 0;
    }

    .about-tabs {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    .about-tab {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background-color: white;
        border: 1px solid var(--border-color);
        border-radius: 2rem;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.875rem;
        color: var(--text-color);
        font-weight: 500;
    }

    .about-tab:hover {
        border-color: var(--primary-color);
        background-color: var(--primary-light);
    }

    .about-tab.active {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .tab-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: var(--primary-light);
        color: var(--primary-color);
        font-size: 0.75rem;
        font-weight: 700;
    }

    .about-tab.active .tab-number {
        background-color: white;
        color: var(--primary-color);
    }

    .about-tab-content-container {
        padding: 2rem;
    }

    .about-tab-content {
        display: none;
    }

    .about-tab-content.active {
        display: block;
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    .about-section-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .about-section-header .progress-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .about-section-header h2 {
        font-size: 1.75rem;
        color: var(--text-color);
        margin: 0;
    }

    .mission-content-wrapper {
        display: flex;
        gap: 2rem;
        align-items: flex-start;
    }

    .mission-image {
        flex: 1;
        max-width: 400px;
        padding: 1rem;
    }

    .about-section-content {
        color: var(--text-muted);
        font-size: 1rem;
        line-height: 1.6;
        flex: 1.5;
    }

    .about-section-content p {
        margin-bottom: 1.5rem;
    }

    /* Mission List */
    .mission-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .mission-list li {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1rem;
        gap: 1rem;
    }

    .list-icon {
        flex-shrink: 0;
    }

    .progress-circle {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }

    .mission-list li span {
        color: var(--text-muted);
        font-size: 1rem;
        line-height: 1.6;
    }

    /* Story Timeline */
    .story-timeline {
        margin-top: 2rem;
    }

    .timeline-item {
        display: flex;
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .timeline-marker {
        flex-shrink: 0;
    }

    .timeline-content {
        flex: 1;
    }

    .timeline-content h3 {
        font-size: 1.25rem;
        color: var(--text-color);
        margin-bottom: 0.5rem;
    }

    .timeline-content p {
        color: var(--text-muted);
        font-size: 0.875rem;
        line-height: 1.6;
        margin-bottom: 0;
    }

    /* Values Section */
    .values-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        width: 100%;
        margin-top: 2rem;
    }

    .value-card {
        background-color: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        display: flex;
        align-items: flex-start;
        gap: 1.5rem;
    }

    .value-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .value-icon {
        flex-shrink: 0;
    }

    .value-content {
        flex: 1;
    }

    .value-content h3 {
        font-size: 1.25rem;
        color: var(--text-color);
        margin-bottom: 0.75rem;
    }

    .value-content p {
        color: var(--text-muted);
        font-size: 0.875rem;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .value-features {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .value-features li {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        color: var(--text-muted);
        font-size: 0.875rem;
    }

    .value-features li i {
        color: var(--primary-color);
        font-size: 0.75rem;
    }

    /* Performance Dashboard Section */
    .dashboard-container {
        margin-top: 2rem;
    }

    .dashboard-svg-container {
        background-color: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        width: 100%;
    }

    .metric-card {
        background-color: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        display: flex;
        align-items: flex-start;
        gap: 1.5rem;
    }

    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .metric-icon {
        flex-shrink: 0;
    }

    .metric-content {
        flex: 1;
    }

    .metric-content h3 {
        font-size: 1.25rem;
        color: var(--text-color);
        margin-bottom: 0.75rem;
    }

    .metric-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .metric-progress {
        margin-top: 0.5rem;
    }

    .progress-bar {
        height: 8px;
        background-color: var(--border-color);
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .progress-fill {
        height: 100%;
        background-color: var(--primary-color);
        border-radius: 4px;
    }

    .metric-progress span {
        font-size: 0.75rem;
        color: var(--text-muted);
    }

    /* Responsive Styles */
    @media (max-width: 1024px) {
        .about-main-hero {
            padding: 6rem 0;
            background-attachment: scroll;
        }

        .hero-content-wrapper {
            padding: 0 2rem;
        }

        .about-main-hero h1 {
            font-size: 3rem;
        }

        .hero-stats {
            gap: 2rem;
        }
    }

    @media (max-width: 992px) {
        .about-hero-section .section-container {
            flex-direction: column;
        }

        .about-hero-content {
            padding-right: 0;
            text-align: center;
        }

        .mission-content-wrapper {
            flex-direction: column;
        }

        .mission-image {
            max-width: 100%;
            order: -1;
            margin-bottom: 2rem;
        }

        .value-card, .metric-card {
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .value-features li, .metric-progress {
            justify-content: center;
        }
    }

    @media (max-width: 768px) {
        .about-main-hero {
            padding: 4rem 0;
            min-height: 60vh;
        }

        .hero-content-wrapper {
            padding: 0 1.5rem;
        }

        .about-main-hero h1 {
            font-size: 2.5rem;
        }

        .about-main-hero p {
            font-size: 1rem;
        }

        .hero-stats {
            flex-direction: column;
            gap: 1.5rem;
        }

        .stat-number {
            font-size: 2rem;
        }

        .about-hero-content h2 {
            font-size: 2.25rem;
        }

        .about-description {
            font-size: 1rem;
        }

        .about-tabs {
            justify-content: center;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // About Tabs
        const aboutTabs = document.querySelectorAll('.about-tab');

        aboutTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs
                aboutTabs.forEach(t => t.classList.remove('active'));

                // Add active class to clicked tab
                tab.classList.add('active');

                // Hide all tab content
                const tabContents = document.querySelectorAll('.about-tab-content');
                tabContents.forEach(content => content.classList.remove('active'));

                // Show the corresponding tab content
                const tabId = tab.getAttribute('data-tab');
                const activeContent = document.getElementById(`${tabId}-content`);
                if (activeContent) {
                    activeContent.classList.add('active');
                }
            });
        });
    });
</script>
