<?php
/**
 * Apply for Loan Page
 *
 * This page shows the guest loan application form for non-logged users
 * and redirects logged-in users to the dashboard loan application.
 */

// Prevent direct access
if (!defined('LENDSWIFT')) {
    die('Direct access to this file is not allowed.');
}

// Check if user is logged in
if (is_user_logged_in()) {
    // Redirect logged-in users to the regular loan application page
    redirect(BASE_URL . '/?page=loan-application');
    exit;
}

// Include the guest loan application form logic
include_once INCLUDES_PATH . '/components/guest_loan_form.php';
?>

<div class="apply-page-wrapper">
    <!-- Apply Hero Section with SVG Design -->
    <section class="apply-hero-section">
        <div class="hero-background">
            <!-- SVG Background Pattern -->
            <svg class="hero-svg" viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:0.8" />
                        <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:0.6" />
                    </linearGradient>
                    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#10B981;stop-opacity:0.7" />
                        <stop offset="100%" style="stop-color:#059669;stop-opacity:0.5" />
                    </linearGradient>
                </defs>

                <!-- Background Shapes -->
                <circle cx="100" cy="100" r="80" fill="url(#gradient1)" opacity="0.3">
                    <animateTransform attributeName="transform" type="rotate" values="0 100 100;360 100 100" dur="20s" repeatCount="indefinite"/>
                </circle>
                <circle cx="1100" cy="150" r="120" fill="url(#gradient2)" opacity="0.2">
                    <animateTransform attributeName="transform" type="rotate" values="360 1100 150;0 1100 150" dur="25s" repeatCount="indefinite"/>
                </circle>
                <polygon points="200,400 300,350 350,450 250,500" fill="url(#gradient1)" opacity="0.2">
                    <animateTransform attributeName="transform" type="rotate" values="0 275 425;360 275 425" dur="30s" repeatCount="indefinite"/>
                </polygon>
                <polygon points="900,400 1000,350 1050,450 950,500" fill="url(#gradient2)" opacity="0.3">
                    <animateTransform attributeName="transform" type="rotate" values="360 975 425;0 975 425" dur="35s" repeatCount="indefinite"/>
                </polygon>

                <!-- Floating Elements -->
                <g opacity="0.4">
                    <circle cx="300" cy="200" r="4" fill="#4F46E5">
                        <animate attributeName="cy" values="200;180;200" dur="3s" repeatCount="indefinite"/>
                    </circle>
                    <circle cx="800" cy="250" r="6" fill="#10B981">
                        <animate attributeName="cy" values="250;230;250" dur="4s" repeatCount="indefinite"/>
                    </circle>
                    <circle cx="500" cy="400" r="5" fill="#7C3AED">
                        <animate attributeName="cy" values="400;380;400" dur="3.5s" repeatCount="indefinite"/>
                    </circle>
                </g>
            </svg>
        </div>

        <div class="apply-hero-content">
            <div class="hero-text">
                <h1>Apply for Your Loan</h1>
                <p>Get started with your loan application. No account required - we'll create one for you upon approval.</p>
                <div class="apply-benefits">
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
                            </svg>
                        </div>
                        <span>Quick 5-minute application</span>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H16V18H8V11H9.2V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.4,8.7 10.4,10V11H13.6V10C13.6,8.7 12.8,8.2 12,8.2Z" fill="currentColor"/>
                            </svg>
                        </div>
                        <span>Secure & confidential</span>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z" fill="currentColor"/>
                            </svg>
                        </div>
                        <span>Instant pre-approval</span>
                    </div>
                </div>
            </div>
            <div class="hero-illustration">
                <svg width="500" height="400" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="formGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
                        </linearGradient>
                    </defs>

                    <!-- Form Illustration -->
                    <rect x="100" y="50" width="300" height="300" rx="20" fill="white" stroke="#E5E7EB" stroke-width="2"/>
                    <rect x="120" y="80" width="260" height="20" rx="10" fill="#F3F4F6"/>
                    <rect x="120" y="120" width="200" height="15" rx="7" fill="#E5E7EB"/>
                    <rect x="120" y="150" width="180" height="15" rx="7" fill="#E5E7EB"/>
                    <rect x="120" y="180" width="220" height="15" rx="7" fill="#E5E7EB"/>
                    <rect x="120" y="220" width="260" height="40" rx="20" fill="url(#formGradient)"/>

                    <!-- Floating Icons -->
                    <circle cx="80" cy="120" r="25" fill="#10B981" opacity="0.8">
                        <animate attributeName="cy" values="120;100;120" dur="3s" repeatCount="indefinite"/>
                    </circle>
                    <text x="80" y="128" text-anchor="middle" fill="white" font-size="16">$</text>

                    <circle cx="420" cy="180" r="20" fill="#F59E0B" opacity="0.8">
                        <animate attributeName="cy" values="180;160;180" dur="4s" repeatCount="indefinite"/>
                    </circle>
                    <text x="420" y="187" text-anchor="middle" fill="white" font-size="14">%</text>

                    <circle cx="60" cy="280" r="18" fill="#EF4444" opacity="0.8">
                        <animate attributeName="cy" values="280;260;280" dur="3.5s" repeatCount="indefinite"/>
                    </circle>
                    <text x="60" y="287" text-anchor="middle" fill="white" font-size="12">✓</text>
                </svg>
            </div>
        </div>
    </section>

    <!-- Full Width Form Section -->
    <section class="apply-form-section">
        <?php
        // Include the guest loan application form HTML
        include_once INCLUDES_PATH . '/components/guest_loan_form_html.php';
        ?>
    </section>
</div>

<style>
    .apply-page-wrapper {
        width: 100%;
        min-height: 100vh;
        overflow-x: hidden;
    }

    /* Hero Section with SVG Design */
    .apply-hero-section {
        position: relative;
        background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
        color: white;
        padding: 6rem 0;
        overflow: hidden;
        min-height: 80vh;
        display: flex;
        align-items: center;
    }

    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
    }

    .hero-svg {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
    }

    .apply-hero-content {
        position: relative;
        z-index: 2;
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 4rem;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
    }

    .hero-text h1 {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 1.5rem;
        line-height: 1.1;
    }

    .hero-text p {
        font-size: 1.25rem;
        margin-bottom: 2.5rem;
        opacity: 0.95;
        line-height: 1.6;
    }

    .apply-benefits {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .benefit-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        color: white;
        font-weight: 500;
        font-size: 1.125rem;
    }

    .benefit-icon {
        width: 48px;
        height: 48px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        flex-shrink: 0;
    }

    .hero-illustration {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .hero-illustration svg {
        max-width: 100%;
        height: auto;
        filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.2));
    }

    /* Full Width Form Section */
    .apply-form-section {
        width: 100%;
        background: #f8fafc;
        padding: 0;
    }

    /* Override guest form styling for full width */
    .guest-application-section {
        padding: 4rem 0;
        background: transparent;
        width: 100%;
    }

    .guest-application-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 4rem;
        width: 100%;
    }

    .guest-form-wrapper {
        width: 100%;
        background: white;
        border-radius: 1.5rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .guest-form-header {
        background: linear-gradient(135deg, #4F46E5, #7C3AED);
        color: white;
        padding: 3rem 4rem 2rem;
        text-align: center;
    }

    .guest-form-header h2 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .guest-form-header p {
        font-size: 1.125rem;
        opacity: 0.9;
    }

    .guest-form-content {
        padding: 4rem;
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .apply-hero-content {
            grid-template-columns: 1fr;
            gap: 3rem;
            text-align: center;
            padding: 0 2rem;
        }

        .hero-text h1 {
            font-size: 2.5rem;
        }

        .guest-application-container {
            padding: 0 2rem;
        }

        .guest-form-content {
            padding: 2rem;
        }
    }

    @media (max-width: 768px) {
        .apply-hero-section {
            padding: 4rem 0;
            min-height: 60vh;
        }

        .apply-hero-content {
            padding: 0 1.5rem;
        }

        .hero-text h1 {
            font-size: 2rem;
        }

        .hero-text p {
            font-size: 1rem;
        }

        .benefit-item {
            font-size: 1rem;
        }

        .benefit-icon {
            width: 40px;
            height: 40px;
        }

        .guest-application-container {
            padding: 0 1.5rem;
        }

        .guest-form-header {
            padding: 2rem 1.5rem 1.5rem;
        }

        .guest-form-header h2 {
            font-size: 1.75rem;
        }

        .guest-form-content {
            padding: 1.5rem;
        }
    }
</style>
