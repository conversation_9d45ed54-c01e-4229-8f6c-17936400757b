<?php
/**
 * Services Page
 *
 * This file contains the services page content.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
if (!defined('LENDSWIFT')) {
    define('LENDSWIFT', true);
}

// Include initialization file
if (!defined('LENDSWIFT')) {
    // If accessed directly, use relative path
    require_once '../includes/init.php';
}

// Get database connection
$db = getDbConnection();
?>

<div class="services-wrapper">
    <?php
    // Get hero section content from database
    $hero_content = [];
    $result = $db->query("SELECT * FROM website_content WHERE section = 'hero' AND page = 'services'");

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $hero_content = [
            'title' => $row['title'],
            'description' => $row['description'],
            'badge' => 'Our Services'
        ];

        // Parse additional content from JSON
        if (!empty($row['content_json'])) {
            $content_json = json_decode($row['content_json'], true);
            if (is_array($content_json)) {
                $hero_content = array_merge($hero_content, $content_json);
            }
        }
    }
    ?>
    <section class="services-hero-section section-light">
        <div class="container">
            <div class="services-hero-content">
                <span class="section-badge"><?php echo htmlspecialchars($hero_content['badge'] ?? 'Our Services'); ?></span>
                <h1><?php echo htmlspecialchars($hero_content['title'] ?? 'Financial Solutions for Every Need'); ?></h1>
                <p class="services-description"><?php echo htmlspecialchars($hero_content['description'] ?? 'Discover our comprehensive range of loan products and financial services designed to help you achieve your goals and secure your financial future.'); ?></p>
            </div>
            <div class="services-hero-image">
                <svg class="services-hero-svg" width="500" height="300" viewBox="0 0 500 300" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <!-- Background shapes -->
                    <circle cx="250" cy="150" r="120" fill="#E0E7FF" opacity="0.5"/>

                    <!-- Document icon -->
                    <rect x="180" y="100" width="140" height="180" rx="10" fill="white" stroke="#4F46E5" stroke-width="3"/>
                    <rect x="200" y="130" width="100" height="10" rx="5" fill="#4F46E5" opacity="0.7"/>
                    <rect x="200" y="150" width="100" height="10" rx="5" fill="#4F46E5" opacity="0.5"/>
                    <rect x="200" y="170" width="80" height="10" rx="5" fill="#4F46E5" opacity="0.3"/>
                    <rect x="200" y="190" width="60" height="10" rx="5" fill="#4F46E5" opacity="0.2"/>

                    <!-- Checkmark -->
                    <circle cx="320" cy="220" r="30" fill="#4F46E5" opacity="0.8"/>
                    <path d="M310 220L318 228L330 212" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>

                    <!-- Dollar sign -->
                    <circle cx="180" cy="220" r="30" fill="#4F46E5" opacity="0.3"/>
                    <path d="M180 210V230" stroke="#4F46E5" stroke-width="3" stroke-linecap="round"/>
                    <path d="M185 215H177.5C175.567 215 174 216.567 174 218.5C174 220.433 175.567 222 177.5 222H182.5C184.433 222 186 223.567 186 225.5C186 227.433 184.433 229 182.5 229H175" stroke="#4F46E5" stroke-width="3" stroke-linecap="round"/>
                </svg>
                <div class="services-shape-1">
                    <svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="100" cy="100" r="100" fill="url(#services-gradient-1)" fill-opacity="0.1"/>
                        <defs>
                            <linearGradient id="services-gradient-1" x1="0" y1="0" x2="200" y2="200" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#4F46E5"/>
                                <stop offset="1" stop-color="#3B82F6"/>
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
                <div class="services-shape-2">
                    <svg width="150" height="150" viewBox="0 0 150 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="75" cy="75" r="75" fill="url(#services-gradient-2)" fill-opacity="0.1"/>
                        <defs>
                            <linearGradient id="services-gradient-2" x1="0" y1="0" x2="150" y2="150" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#3B82F6"/>
                                <stop offset="1" stop-color="#4F46E5"/>
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
            </div>
        </div>
    </section>

    <section class="our-mission-section section-primary-light">
        <div class="container">
            <div class="mission-content-wrapper">
                <div class="mission-content">
                    <div class="section-header text-left">
                        <span class="section-badge">Our Mission</span>
                        <h2>Empowering Your Financial Journey</h2>
                        <p>We're committed to providing accessible, transparent, and personalized loan solutions that help you achieve your financial goals.</p>
                    </div>
                    <div class="mission-list-container">
                        <ul class="mission-list">
                            <li>
                                <div class="list-icon">
                                    <div class="progress-circle">
                                        <i class="fas fa-check"></i>
                                    </div>
                                </div>
                                <span>Providing fast and accessible loan solutions for everyone</span>
                            </li>
                            <li>
                                <div class="list-icon">
                                    <div class="progress-circle">
                                        <i class="fas fa-check"></i>
                                    </div>
                                </div>
                                <span>Ensuring complete transparency in all our loan processes</span>
                            </li>
                            <li>
                                <div class="list-icon">
                                    <div class="progress-circle">
                                        <i class="fas fa-check"></i>
                                    </div>
                                </div>
                                <span>Offering competitive rates and flexible repayment options</span>
                            </li>
                            <li>
                                <div class="list-icon">
                                    <div class="progress-circle">
                                        <i class="fas fa-check"></i>
                                    </div>
                                </div>
                                <span>Delivering exceptional customer service and support</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="mission-image">
                    <svg width="100%" height="100%" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background Circle -->
                        <circle cx="200" cy="200" r="180" fill="#E0E7FF" opacity="0.5"/>

                        <!-- Target Icon -->
                        <circle cx="200" cy="200" r="150" stroke="#4F46E5" stroke-width="2" stroke-dasharray="4 4"/>
                        <circle cx="200" cy="200" r="120" stroke="#4F46E5" stroke-width="2" stroke-dasharray="4 4"/>
                        <circle cx="200" cy="200" r="90" stroke="#4F46E5" stroke-width="2" stroke-dasharray="4 4"/>
                        <circle cx="200" cy="200" r="60" stroke="#4F46E5" stroke-width="2" stroke-dasharray="4 4"/>
                        <circle cx="200" cy="200" r="30" fill="#4F46E5" opacity="0.8"/>

                        <!-- Arrow -->
                        <path d="M320 200L350 200" stroke="#4F46E5" stroke-width="2" stroke-linecap="round"/>
                        <path d="M340 190L350 200L340 210" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>

                        <!-- Dollar Sign -->
                        <circle cx="200" cy="200" r="15" fill="#FFFFFF"/>
                        <path d="M200 190V210" stroke="#4F46E5" stroke-width="2" stroke-linecap="round"/>
                        <path d="M205 195H197.5C195.567 195 194 196.567 194 198.5C194 200.433 195.567 202 197.5 202H202.5C204.433 202 206 203.567 206 205.5C206 207.433 204.433 209 202.5 209H195" stroke="#4F46E5" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </div>
            </div>
        </div>
    </section>





    <section class="additional-services-section section-darker">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">Additional Services</span>
                <h2>Beyond Loans</h2>
                <p>We offer more than just loans to help you manage your finances effectively</p>
            </div>

            <div class="additional-services-grid">
                <div class="additional-service-card">
                    <div class="service-image">
                        <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 023.webp" alt="Loan Refinancing" style="width: 100%; height: 200px; object-fit: cover;">
                        <div class="service-overlay">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                    </div>
                    <div class="service-content">
                        <h3>Loan Refinancing</h3>
                        <p>Refinance your existing loans to get better interest rates and more favorable terms. Our refinancing options can help you lower your monthly payments and save money over time.</p>
                        <a href="#" class="button button-primary">Learn More</a>
                    </div>
                </div>

                <div class="additional-service-card">
                    <div class="service-image">
                        <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 024.webp" alt="Financial Planning" style="width: 100%; height: 200px; object-fit: cover;">
                        <div class="service-overlay">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="service-content">
                        <h3>Financial Planning</h3>
                        <p>Get expert advice on managing your finances and planning for your future goals. Our financial advisors can help you create a personalized plan to achieve financial stability and growth.</p>
                        <a href="#" class="button button-primary">Learn More</a>
                    </div>
                </div>

                <div class="additional-service-card">
                    <div class="service-image">
                        <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 025.webp" alt="Payment Scheduling" style="width: 100%; height: 200px; object-fit: cover;">
                        <div class="service-overlay">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                    </div>
                    <div class="service-content">
                        <h3>Payment Scheduling</h3>
                        <p>Flexible payment scheduling options to help you manage your loan repayments. Set up automatic payments, choose your payment date, and adjust your payment schedule as needed to fit your budget.</p>
                        <a href="#" class="button button-primary">Learn More</a>
                    </div>
                </div>

                <div class="additional-service-card">
                    <div class="service-image">
                        <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 026.webp" alt="Customer Support" style="width: 100%; height: 200px; object-fit: cover;">
                        <div class="service-overlay">
                            <i class="fas fa-headset"></i>
                        </div>
                    </div>
                    <div class="service-content">
                        <h3>Customer Support</h3>
                        <p>Dedicated customer support to assist you throughout your loan journey. Our team is available to answer your questions, provide guidance, and help you navigate the loan process from application to repayment.</p>
                        <a href="#" class="button button-primary">Learn More</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="testimonials-section section-primary-light">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">Customer Stories</span>
                <h2>What Our Clients Say</h2>
                <p>Don't just take our word for it - hear from our satisfied customers</p>
            </div>

            <div class="testimonials-slider">
                <div class="testimonial-card">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="testimonial-text">"The loan application process was incredibly easy. Their online application took me less than 10 minutes to complete, and I had approval within 24 hours. The funds were in my account the next day!"</p>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="25" cy="25" r="25" fill="#4F46E5" opacity="0.1"/>
                                <circle cx="25" cy="20" r="10" fill="#4F46E5" opacity="0.3"/>
                                <path d="M15 40C15 34.4772 19.4772 30 25 30C30.5228 30 35 34.4772 35 40" fill="#4F46E5" opacity="0.3"/>
                                <text x="25" y="22" text-anchor="middle" font-size="10" fill="#4F46E5" font-weight="bold">JD</text>
                            </svg>
                        </div>
                        <div class="author-info">
                            <h4>John D.</h4>
                            <p>Business Owner</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="testimonial-text">"I needed a personal loan for home renovations and was dreading the paperwork. The process was completely digital and straightforward. Their customer service team was also incredibly helpful when I had questions."</p>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="25" cy="25" r="25" fill="#4F46E5" opacity="0.1"/>
                                <circle cx="25" cy="20" r="10" fill="#4F46E5" opacity="0.3"/>
                                <path d="M15 40C15 34.4772 19.4772 30 25 30C30.5228 30 35 34.4772 35 40" fill="#4F46E5" opacity="0.3"/>
                                <text x="25" y="22" text-anchor="middle" font-size="10" fill="#4F46E5" font-weight="bold">SM</text>
                            </svg>
                        </div>
                        <div class="author-info">
                            <h4>Sarah M.</h4>
                            <p>Homeowner</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    </div>
                    <p class="testimonial-text">"The interest rates were competitive and the terms were clear. I appreciated the transparency throughout the process. No hidden fees or surprises. I'll definitely use their services again in the future."</p>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="25" cy="25" r="25" fill="#4F46E5" opacity="0.1"/>
                                <circle cx="25" cy="20" r="10" fill="#4F46E5" opacity="0.3"/>
                                <path d="M15 40C15 34.4772 19.4772 30 25 30C30.5228 30 35 34.4772 35 40" fill="#4F46E5" opacity="0.3"/>
                                <text x="25" y="22" text-anchor="middle" font-size="10" fill="#4F46E5" font-weight="bold">MT</text>
                            </svg>
                        </div>
                        <div class="author-info">
                            <h4>Michael T.</h4>
                            <p>Financial Analyst</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p class="testimonial-text">"As a small business owner, I needed quick funding to expand my operations. Their business loan process was efficient and the customer service was exceptional. The financial advisor helped me choose the right loan product for my needs."</p>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="25" cy="25" r="25" fill="#4F46E5" opacity="0.1"/>
                                <circle cx="25" cy="20" r="10" fill="#4F46E5" opacity="0.3"/>
                                <path d="M15 40C15 34.4772 19.4772 30 25 30C30.5228 30 35 34.4772 35 40" fill="#4F46E5" opacity="0.3"/>
                                <text x="25" y="22" text-anchor="middle" font-size="10" fill="#4F46E5" font-weight="bold">RL</text>
                            </svg>
                        </div>
                        <div class="author-info">
                            <h4>Rebecca L.</h4>
                            <p>Retail Shop Owner</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="testimonial-indicators">
                <span class="indicator active"></span>
                <span class="indicator"></span>
                <span class="indicator"></span>
                <span class="indicator"></span>
            </div>
        </div>
    </section>

<style>
    /* Services Page Styles */
    .services-wrapper {
        width: 100%;
        overflow-x: hidden;
        margin: 0;
        padding: 0;
    }

    /* Section Background Colors */
    .section-light {
        background-color: #ffffff;
    }

    .section-primary-light {
        background-color: #f0f5ff; /* Very light bluish */
    }

    .section-darker {
        background-color: #f7f9fc; /* Lighter */
    }

    /* Hero Section */
    .services-hero-section {
        padding: 5rem 0;
        position: relative;
        overflow: hidden;
        width: 100%;
    }

    .services-hero-section .container {
        display: flex;
        align-items: center;
        gap: 2rem;
        width: 100%;
        max-width: 100%;
        padding: 0 4rem;
        box-sizing: border-box;
    }

    .services-hero-content {
        flex: 1;
        padding-right: 2rem;
    }

    .services-hero-content h1 {
        font-size: 3rem;
        color: var(--text-color);
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .services-description {
        font-size: 1.25rem;
        color: var(--text-muted);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .services-hero-image {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }

    .services-hero-svg {
        max-width: 100%;
        height: auto;
        position: relative;
        z-index: 2;
    }

    .services-shape-1, .services-shape-2 {
        position: absolute;
        z-index: 1;
    }

    .services-shape-1 {
        top: -50px;
        right: -30px;
    }

    .services-shape-2 {
        bottom: -30px;
        left: -20px;
    }

    /* Mission Section */
    .our-mission-section {
        padding: 5rem 0;
        width: 100%;
    }

    .our-mission-section .container {
        width: 100%;
        max-width: 100%;
        padding: 0 4rem;
        box-sizing: border-box;
    }

    .mission-content-wrapper {
        display: flex;
        gap: 2rem;
        align-items: flex-start;
    }

    .mission-content {
        flex: 1.5;
    }

    .section-header.text-left {
        text-align: left;
        margin-bottom: 2rem;
    }

    .mission-list-container {
        margin-top: 2rem;
    }

    .mission-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .mission-list li {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1rem;
        gap: 1rem;
    }

    .list-icon {
        flex-shrink: 0;
    }

    .progress-circle {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }

    .mission-list li span {
        color: var(--text-muted);
        font-size: 1rem;
        line-height: 1.6;
    }

    .mission-image {
        flex: 1;
        max-width: 400px;
        padding: 1rem;
    }

    /* Services List Section */
    .services-list-section {
        padding: 5rem 0;
        width: 100%;
    }

    .services-list-section .container {
        width: 100%;
        max-width: 100%;
        padding: 0 4rem;
        box-sizing: border-box;
    }

    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .service-card {
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .service-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .service-icon {
        display: flex;
        justify-content: center;
        padding: 2rem 0;
        background-color: var(--primary-light);
    }

    .icon-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }

    .service-content {
        padding: 2rem;
    }

    .service-content h3 {
        font-size: 1.5rem;
        color: var(--text-color);
        margin-bottom: 1rem;
    }

    .service-content p {
        color: var(--text-muted);
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }

    .service-details {
        background-color: var(--primary-light);
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.75rem;
    }

    .detail-item:last-child {
        margin-bottom: 0;
    }

    .detail-label {
        font-weight: 600;
        color: var(--text-color);
    }

    .detail-value {
        color: var(--primary-color);
        font-weight: 600;
    }

    .service-content .button {
        width: 100%;
        text-align: center;
    }

    /* Loan Process Section */
    .loan-process-section {
        padding: 5rem 0;
        width: 100%;
    }

    .loan-process-section .container {
        width: 100%;
        max-width: 100%;
        padding: 0 4rem;
        box-sizing: border-box;
    }

    .steps-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        margin-top: 3rem;
    }

    .step-card {
        background-color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .step-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .step-number-container {
        margin-bottom: 1rem;
    }

    .step-number {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        font-weight: 700;
        margin: 0 auto;
    }

    .step-icon {
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .step-content {
        width: 100%;
    }

    .step-content h3 {
        font-size: 1.25rem;
        color: var(--text-color);
        margin-bottom: 0.75rem;
    }

    .step-content p {
        color: var(--text-muted);
        font-size: 0.875rem;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .step-features {
        list-style: none;
        padding: 0;
        margin: 0;
        text-align: left;
    }

    .step-features li {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        color: var(--text-muted);
        font-size: 0.875rem;
    }

    .step-features li i {
        color: var(--primary-color);
        font-size: 0.75rem;
    }

    /* Additional Services Section */
    .additional-services-section {
        padding: 5rem 0;
        width: 100%;
    }

    .additional-services-section .container {
        width: 100%;
        max-width: 100%;
        padding: 0 4rem;
        box-sizing: border-box;
    }

    .additional-services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .additional-service-card {
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .additional-service-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .additional-service-card .service-image {
        position: relative;
        height: 200px;
        overflow: hidden;
    }

    .additional-service-card .service-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .additional-service-card:hover .service-image img {
        transform: scale(1.05);
    }

    .additional-service-card .service-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.8), rgba(124, 58, 237, 0.8));
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .additional-service-card:hover .service-overlay {
        opacity: 1;
    }

    .additional-service-card .service-overlay i {
        color: white;
        font-size: 2rem;
    }

    .additional-service-card .service-content {
        padding: 2rem;
    }

    .additional-service-card .service-content h3 {
        font-size: 1.5rem;
        color: var(--text-color);
        margin-bottom: 1rem;
    }

    .additional-service-card .service-content p {
        color: var(--text-muted);
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }

    .additional-service-card .service-content .button {
        width: 100%;
        text-align: center;
    }

    /* Testimonials Section */
    .testimonials-section {
        padding: 5rem 0;
        width: 100%;
    }

    .testimonials-section .container {
        width: 100%;
        max-width: 100%;
        padding: 0 4rem;
        box-sizing: border-box;
    }

    .testimonials-slider {
        display: flex;
        gap: 2rem;
        width: 100%;
        margin-bottom: 2rem;
        overflow-x: auto;
        padding: 1rem 0.5rem;
        scrollbar-width: none; /* Firefox */
    }

    .testimonials-slider::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
    }

    .testimonial-card {
        flex: 0 0 calc(33.333% - 1.5rem);
        min-width: 300px;
        background-color: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .testimonial-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .testimonial-rating {
        color: #f59e0b;
        margin-bottom: 1rem;
    }

    .testimonial-text {
        color: var(--text-color);
        font-size: 0.875rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
        font-style: italic;
    }

    .testimonial-author {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .author-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
    }

    .author-avatar svg {
        width: 100%;
        height: 100%;
    }

    .author-info h4 {
        font-size: 1rem;
        color: var(--text-color);
        margin: 0 0 0.25rem 0;
    }

    .author-info p {
        font-size: 0.75rem;
        color: var(--text-muted);
        margin: 0;
    }

    .testimonial-indicators {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
    }

    .indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #e5e7eb;
        cursor: pointer;
    }

    .indicator.active {
        background-color: var(--primary-color);
    }

    /* Responsive Styles */
    @media (max-width: 992px) {
        .services-hero-section .container,
        .our-mission-section .container,
        .services-list-section .container,
        .loan-process-section .container,
        .additional-services-section .container,
        .testimonials-section .container {
            flex-direction: column;
            padding: 0 2rem;
        }

        .services-hero-content {
            padding-right: 0;
            text-align: center;
        }

        .mission-content-wrapper {
            flex-direction: column;
        }

        .mission-image {
            max-width: 100%;
            order: -1;
            margin-bottom: 2rem;
        }

        .services-grid {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }

        .steps-container {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .services-hero-section .container,
        .our-mission-section .container,
        .services-list-section .container,
        .loan-process-section .container,
        .additional-services-section .container,
        .testimonials-section .container {
            padding: 0 1rem;
        }

        .services-hero-content h1 {
            font-size: 2.25rem;
        }

        .services-description {
            font-size: 1rem;
        }

        .services-grid {
            grid-template-columns: 1fr;
        }

        .additional-services-grid {
            grid-template-columns: 1fr;
        }

        .testimonials-slider {
            grid-template-columns: 1fr;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation to testimonial cards
        const testimonialCards = document.querySelectorAll('.testimonial-card');

        if (testimonialCards.length > 0) {
            // Add animation on scroll
            window.addEventListener('scroll', () => {
                testimonialCards.forEach(card => {
                    const cardPosition = card.getBoundingClientRect().top;
                    const screenPosition = window.innerHeight / 1.3;

                    if (cardPosition < screenPosition) {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }
                });
            });

            // Initialize cards with animation properties
            testimonialCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            });

            // Trigger initial check
            window.dispatchEvent(new Event('scroll'));
        }

        // Testimonial Slider
        const indicators = document.querySelectorAll('.testimonial-indicators .indicator');
        const slider = document.querySelector('.testimonials-slider');

        if (indicators.length > 0 && slider) {
            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => {
                    // Update active indicator
                    indicators.forEach(ind => ind.classList.remove('active'));
                    indicator.classList.add('active');

                    // Scroll to corresponding testimonial
                    const testimonials = slider.querySelectorAll('.testimonial-card');
                    if (testimonials[index]) {
                        const scrollPosition = testimonials[index].offsetLeft - slider.offsetLeft;
                        slider.scrollTo({
                            left: scrollPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }

        // Add hover effect to additional service items
        const serviceItems = document.querySelectorAll('.additional-service-item');
        if (serviceItems.length > 0) {
            serviceItems.forEach(item => {
                item.addEventListener('mouseenter', () => {
                    item.classList.add('hovered');
                });

                item.addEventListener('mouseleave', () => {
                    item.classList.remove('hovered');
                });
            });
        }
    });
</script>
